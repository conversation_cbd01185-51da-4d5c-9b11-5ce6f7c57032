#!/bin/bash

# Hummingbot Deployment Script
# This script helps deploy Hummingbot with network connectivity workarounds

echo "=== Hummingbot Deployment Script ==="
echo "Date: $(date)"
echo "===================================="

# Function to check if <PERSON><PERSON> is running
check_docker() {
    echo "Checking Docker status..."
    if ! docker info >/dev/null 2>&1; then
        echo "❌ Docker is not running!"
        echo "Please start Docker Desktop and try again."
        echo "Steps:"
        echo "1. Open Docker Desktop from Start Menu"
        echo "2. Wait for Docker to fully start (green icon in system tray)"
        echo "3. Run this script again"
        exit 1
    else
        echo "✅ Docker is running"
    fi
}

# Function to create necessary directories
create_directories() {
    echo "Creating necessary directories..."
    mkdir -p credentials
    mkdir -p data
    mkdir -p logs
    echo "✅ Directories created"
}

# Function to check network connectivity
check_network() {
    echo "Checking network connectivity..."
    
    # Test Docker Hub connectivity
    if docker pull hello-world >/dev/null 2>&1; then
        echo "✅ Docker Hub connectivity: OK"
        docker rmi hello-world >/dev/null 2>&1
        return 0
    else
        echo "❌ Docker Hub connectivity: FAILED"
        echo "⚠️  Network issues detected. Will attempt alternative methods."
        return 1
    fi
}

# Function to pull Docker images with retry
pull_images() {
    echo "Pulling Hummingbot Docker images..."
    
    images=(
        "hummingbot/dashboard:latest"
        "hummingbot/backend-api:latest" 
        "hummingbot/broker:latest"
    )
    
    for image in "${images[@]}"; do
        echo "Pulling $image..."
        retry_count=0
        max_retries=3
        
        while [ $retry_count -lt $max_retries ]; do
            if docker pull "$image"; then
                echo "✅ Successfully pulled $image"
                break
            else
                retry_count=$((retry_count + 1))
                echo "❌ Failed to pull $image (attempt $retry_count/$max_retries)"
                if [ $retry_count -lt $max_retries ]; then
                    echo "Retrying in 10 seconds..."
                    sleep 10
                fi
            fi
        done
        
        if [ $retry_count -eq $max_retries ]; then
            echo "❌ Failed to pull $image after $max_retries attempts"
            echo "This may be due to network restrictions."
            echo "Please check the NETWORK_WORKAROUND_SETUP.md file for alternatives."
        fi
    done
}

# Function to start services
start_services() {
    echo "Starting Hummingbot services..."
    
    # Stop any existing containers
    echo "Stopping existing containers..."
    docker compose down >/dev/null 2>&1
    
    # Start services
    echo "Starting new containers..."
    if docker compose up -d; then
        echo "✅ Services started successfully"
        return 0
    else
        echo "❌ Failed to start services"
        return 1
    fi
}

# Function to check service health
check_services() {
    echo "Checking service health..."
    sleep 10  # Wait for services to start
    
    services=("dashboard" "backend-api" "hummingbot-broker")
    
    for service in "${services[@]}"; do
        if docker ps | grep -q "$service"; then
            echo "✅ $service: Running"
        else
            echo "❌ $service: Not running"
            echo "Checking logs for $service:"
            docker logs "$service" 2>/dev/null | tail -5
        fi
    done
}

# Function to display access information
show_access_info() {
    echo ""
    echo "=== Access Information ==="
    echo "Dashboard URL: http://localhost:8501"
    echo "Backend API: http://localhost:8000"
    echo "Broker Dashboard: http://localhost:8083"
    echo ""
    echo "If using a cloud server, replace 'localhost' with your server IP"
    echo ""
    echo "=== Next Steps ==="
    echo "1. Open http://localhost:8501 in your browser"
    echo "2. Configure exchange credentials"
    echo "3. Create your first trading strategy"
    echo "4. Start with paper trading to test"
    echo ""
    echo "=== Useful Commands ==="
    echo "View logs: docker compose logs -f"
    echo "Stop services: docker compose down"
    echo "Restart services: docker compose restart"
    echo "Check status: docker ps"
}

# Function to handle network issues
handle_network_issues() {
    echo ""
    echo "=== Network Issues Detected ==="
    echo "Your network appears to have restrictions accessing Docker Hub."
    echo ""
    echo "Possible solutions:"
    echo "1. Use VPN or proxy if available"
    echo "2. Contact IT department for Docker Hub access"
    echo "3. Use mobile hotspot temporarily"
    echo "4. Load Docker images from files (if available)"
    echo ""
    echo "For detailed workarounds, see: NETWORK_WORKAROUND_SETUP.md"
    echo ""
    read -p "Do you want to try starting with existing images? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        start_services
        if [ $? -eq 0 ]; then
            check_services
            show_access_info
        fi
    fi
}

# Main execution
main() {
    echo "Starting Hummingbot deployment..."
    
    # Step 1: Check Docker
    check_docker
    
    # Step 2: Create directories
    create_directories
    
    # Step 3: Check network and pull images
    if check_network; then
        pull_images
        start_services
        check_services
        show_access_info
    else
        handle_network_issues
    fi
    
    echo ""
    echo "Deployment script completed!"
    echo "For support, visit: https://discord.gg/hummingbot"
}

# Run main function
main
