version: '3.8'

services:
  dashboard:
    image: hummingbot/dashboard:latest
    container_name: dashboard
    ports:
      - "8501:8501"
    environment:
      - BACKEND_API_HOST=backend-api
      - BACKEND_API_PORT=8000
      - STREAMLIT_SERVER_HEADLESS=true
      - STREAMLIT_SERVER_ENABLE_CORS=false
    depends_on:
      - backend-api
    networks:
      - hummingbot-network
    restart: unless-stopped
    volumes:
      - dashboard_data:/app/data

  backend-api:
    image: hummingbot/backend-api:latest
    container_name: backend-api
    ports:
      - "8000:8000"
    environment:
      - BROKER_HOST=hummingbot-broker
      - BROKER_PORT=1883
      - DATABASE_URL=sqlite:///./backend.db
    depends_on:
      - hummingbot-broker
    networks:
      - hummingbot-network
    restart: unless-stopped
    volumes:
      - backend_data:/app/data
      - ./credentials:/app/credentials

  hummingbot-broker:
    image: hummingbot/broker:latest
    container_name: hummingbot-broker
    ports:
      - "1883:1883"
      - "8083:8083"
      - "18083:18083"
    environment:
      - EMQX_LOADED_PLUGINS="emqx_recon,emqx_retainer,emqx_management,emqx_dashboard"
    networks:
      - hummingbot-network
    restart: unless-stopped
    volumes:
      - broker_data:/opt/emqx/data
      - broker_log:/opt/emqx/log

  # Optional: Gateway for DEX trading
  gateway:
    image: hummingbot/gateway:latest
    container_name: hummingbot-gateway
    ports:
      - "15888:15888"
    environment:
      - GATEWAY_PASSPHRASE=your_gateway_passphrase_here
      - GATEWAY_CERT_PASSPHRASE=your_cert_passphrase_here
    networks:
      - hummingbot-network
    restart: unless-stopped
    volumes:
      - gateway_data:/app/conf
      - gateway_logs:/app/logs
    profiles:
      - gateway

networks:
  hummingbot-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  dashboard_data:
    driver: local
  backend_data:
    driver: local
  broker_data:
    driver: local
  broker_log:
    driver: local
  gateway_data:
    driver: local
  gateway_logs:
    driver: local
