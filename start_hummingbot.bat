@echo off
REM Hummingbot Deployment Script for Windows
REM This script helps deploy Hummingbot with network connectivity workarounds

echo === Hummingbot Deployment Script ===
echo Date: %date% %time%
echo ====================================

echo.
echo Step 1: Checking Docker status...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running!
    echo Please start Docker Desktop and try again.
    echo Steps:
    echo 1. Open Docker Desktop from Start Menu
    echo 2. Wait for Docker to fully start ^(green icon in system tray^)
    echo 3. Run this script again
    pause
    exit /b 1
) else (
    echo ✅ Docker is running
)

echo.
echo Step 2: Creating necessary directories...
if not exist "credentials" mkdir credentials
if not exist "data" mkdir data
if not exist "logs" mkdir logs
echo ✅ Directories created

echo.
echo Step 3: Checking network connectivity...
docker pull hello-world >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Docker Hub connectivity: OK
    docker rmi hello-world >nul 2>&1
    goto :pull_images
) else (
    echo ❌ Docker Hub connectivity: FAILED
    echo ⚠️  Network issues detected. Will attempt alternative methods.
    goto :handle_network_issues
)

:pull_images
echo.
echo Step 4: Pulling Hummingbot Docker images...

echo Pulling hummingbot/dashboard:latest...
docker pull hummingbot/dashboard:latest
if %errorlevel% neq 0 (
    echo ❌ Failed to pull dashboard image
    goto :handle_network_issues
) else (
    echo ✅ Successfully pulled dashboard image
)

echo Pulling hummingbot/backend-api:latest...
docker pull hummingbot/backend-api:latest
if %errorlevel% neq 0 (
    echo ❌ Failed to pull backend-api image
    goto :handle_network_issues
) else (
    echo ✅ Successfully pulled backend-api image
)

echo Pulling hummingbot/broker:latest...
docker pull hummingbot/broker:latest
if %errorlevel% neq 0 (
    echo ❌ Failed to pull broker image
    goto :handle_network_issues
) else (
    echo ✅ Successfully pulled broker image
)

goto :start_services

:start_services
echo.
echo Step 5: Starting Hummingbot services...

echo Stopping existing containers...
docker compose down >nul 2>&1

echo Starting new containers...
docker compose up -d
if %errorlevel% neq 0 (
    echo ❌ Failed to start services
    echo Checking for issues...
    docker compose logs
    pause
    exit /b 1
) else (
    echo ✅ Services started successfully
)

goto :check_services

:check_services
echo.
echo Step 6: Checking service health...
timeout /t 10 /nobreak >nul

docker ps | findstr "dashboard" >nul
if %errorlevel% equ 0 (
    echo ✅ dashboard: Running
) else (
    echo ❌ dashboard: Not running
)

docker ps | findstr "backend-api" >nul
if %errorlevel% equ 0 (
    echo ✅ backend-api: Running
) else (
    echo ❌ backend-api: Not running
)

docker ps | findstr "hummingbot-broker" >nul
if %errorlevel% equ 0 (
    echo ✅ hummingbot-broker: Running
) else (
    echo ❌ hummingbot-broker: Not running
)

goto :show_access_info

:show_access_info
echo.
echo === Access Information ===
echo Dashboard URL: http://localhost:8501
echo Backend API: http://localhost:8000
echo Broker Dashboard: http://localhost:8083
echo.
echo If using a cloud server, replace 'localhost' with your server IP
echo.
echo === Next Steps ===
echo 1. Open http://localhost:8501 in your browser
echo 2. Configure exchange credentials
echo 3. Create your first trading strategy
echo 4. Start with paper trading to test
echo.
echo === Useful Commands ===
echo View logs: docker compose logs -f
echo Stop services: docker compose down
echo Restart services: docker compose restart
echo Check status: docker ps
echo.
echo Deployment completed successfully!
echo For support, visit: https://discord.gg/hummingbot
pause
exit /b 0

:handle_network_issues
echo.
echo === Network Issues Detected ===
echo Your network appears to have restrictions accessing Docker Hub.
echo.
echo Possible solutions:
echo 1. Use VPN or proxy if available
echo 2. Contact IT department for Docker Hub access
echo 3. Use mobile hotspot temporarily
echo 4. Load Docker images from files ^(if available^)
echo.
echo For detailed workarounds, see: NETWORK_WORKAROUND_SETUP.md
echo.
set /p choice="Do you want to try starting with existing images? (y/n): "
if /i "%choice%"=="y" goto :start_services
if /i "%choice%"=="yes" goto :start_services

echo.
echo Please resolve network issues and try again.
echo Check the troubleshooting guides for more help.
pause
exit /b 1
