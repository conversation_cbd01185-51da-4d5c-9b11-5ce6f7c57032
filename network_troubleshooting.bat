@echo off
REM Hummingbot Network Troubleshooting Script for Windows
REM This script helps diagnose and resolve network connectivity issues

echo === Hummingbot Network Troubleshooting ===
echo Date: %date% %time%
echo ==========================================

echo.
echo 1. Testing basic connectivity...
echo Testing Google connectivity...
curl -s --connect-timeout 10 --max-time 30 "https://www.google.com" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Google: SUCCESS
) else (
    echo ❌ Google: FAILED
)

echo Testing GitHub connectivity...
curl -s --connect-timeout 10 --max-time 30 "https://github.com" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ GitHub: SUCCESS
) else (
    echo ❌ GitHub: FAILED
)

echo Testing Docker Hub connectivity...
curl -s --connect-timeout 10 --max-time 30 "https://docker.io" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Docker Hub: SUCCESS
) else (
    echo ❌ Docker Hub: FAILED
)

echo.
echo 2. Testing DNS resolution...
echo Testing github.com DNS...
nslookup github.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ github.com DNS: SUCCESS
) else (
    echo ❌ github.com DNS: FAILED
)

echo Testing hummingbot.org DNS...
nslookup hummingbot.org >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ hummingbot.org DNS: SUCCESS
) else (
    echo ❌ hummingbot.org DNS: FAILED
)

echo.
echo 3. Testing specific Hummingbot resources...
echo Testing Hummingbot Repository...
curl -s --connect-timeout 10 --max-time 30 "https://github.com/hummingbot/hummingbot" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Hummingbot Repository: SUCCESS
) else (
    echo ❌ Hummingbot Repository: FAILED
)

echo Testing Deploy Repository...
curl -s --connect-timeout 10 --max-time 30 "https://github.com/hummingbot/deploy" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Deploy Repository: SUCCESS
) else (
    echo ❌ Deploy Repository: FAILED
)

echo.
echo 4. System Information...
echo Operating System: %OS%
docker --version 2>nul
if %errorlevel% equ 0 (
    docker --version
) else (
    echo Docker: Not installed
)

git --version 2>nul
if %errorlevel% equ 0 (
    git --version
) else (
    echo Git: Not installed
)

curl --version 2>nul | findstr "curl"
if %errorlevel% equ 0 (
    curl --version | findstr "curl"
) else (
    echo Curl: Not installed
)

echo.
echo 5. Network Configuration...
echo Default Gateway:
ipconfig | findstr "Default Gateway"

echo DNS Servers:
ipconfig /all | findstr "DNS Servers"

echo.
echo === Troubleshooting Recommendations ===
echo.
echo If you're experiencing connectivity issues:
echo.
echo ❌ GitHub connectivity issues:
echo    • Check your internet connection
echo    • Try using a VPN
echo    • Check if GitHub is blocked by your network
echo    • Try alternative download methods below
echo.
echo ❌ DNS resolution issues:
echo    • Try using public DNS servers (*******, *******)
echo    • Flush DNS cache: ipconfig /flushdns
echo    • Restart network adapter
echo.
echo === Alternative Download Methods ===
echo.
echo 1. Download as ZIP (Main Repository):
echo    curl -L -o hummingbot.zip https://github.com/hummingbot/hummingbot/archive/refs/heads/master.zip
echo    tar -xf hummingbot.zip
echo    move hummingbot-master\* .
echo.
echo 2. Download as ZIP (Deploy Repository):
echo    curl -L -o deploy.zip https://github.com/hummingbot/deploy/archive/refs/heads/main.zip
echo    tar -xf deploy.zip
echo    move deploy-main\* .
echo.
echo 3. Use shallow clone (faster):
echo    git clone --depth 1 https://github.com/hummingbot/hummingbot.git
echo.
echo === Docker Troubleshooting ===
echo.
echo If Docker issues occur:
echo • Ensure Docker Desktop is running
echo • Check available disk space
echo • Restart Docker Desktop
echo • Clear Docker cache: docker system prune -a
echo • Check Docker logs: docker logs ^<container_name^>
echo.
echo === Port Requirements ===
echo.
echo Ensure these ports are available:
echo • 8501 - Hummingbot Dashboard
echo • 8000 - Backend API  
echo • 15888 - Gateway (for DEX trading)
echo • 1883 - MQTT Broker
echo.
echo Troubleshooting complete. Check the recommendations above.
echo For additional help, visit: https://discord.gg/hummingbot
echo.
pause
