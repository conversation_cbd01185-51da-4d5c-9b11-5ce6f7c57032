#!/bin/bash

# Hummingbot Network Troubleshooting Script
# This script helps diagnose and resolve network connectivity issues

echo "=== Hummingbot Network Troubleshooting ==="
echo "Date: $(date)"
echo "=========================================="

# Function to test connectivity
test_connectivity() {
    local url=$1
    local name=$2
    echo -n "Testing $name connectivity... "
    if curl -s --connect-timeout 10 --max-time 30 "$url" > /dev/null; then
        echo "✅ SUCCESS"
        return 0
    else
        echo "❌ FAILED"
        return 1
    fi
}

# Function to test DNS resolution
test_dns() {
    local domain=$1
    echo -n "Testing DNS resolution for $domain... "
    if nslookup "$domain" > /dev/null 2>&1; then
        echo "✅ SUCCESS"
        return 0
    else
        echo "❌ FAILED"
        return 1
    fi
}

echo "1. Testing basic connectivity..."
test_connectivity "https://www.google.com" "Google"
test_connectivity "https://github.com" "GitHub"
test_connectivity "https://docker.io" "Docker Hub"

echo ""
echo "2. Testing DNS resolution..."
test_dns "github.com"
test_dns "hummingbot.org"
test_dns "docker.io"

echo ""
echo "3. Testing specific Hummingbot resources..."
test_connectivity "https://github.com/hummingbot/hummingbot" "Hummingbot Repository"
test_connectivity "https://github.com/hummingbot/deploy" "Deploy Repository"
test_connectivity "https://hummingbot.org" "Hummingbot Website"

echo ""
echo "4. System Information..."
echo "Operating System: $(uname -a)"
echo "Docker Version: $(docker --version 2>/dev/null || echo 'Not installed')"
echo "Git Version: $(git --version 2>/dev/null || echo 'Not installed')"
echo "Curl Version: $(curl --version 2>/dev/null | head -1 || echo 'Not installed')"

echo ""
echo "5. Network Configuration..."
echo "Default Gateway: $(ip route | grep default | awk '{print $3}' 2>/dev/null || route -n | grep '^0.0.0.0' | awk '{print $2}' 2>/dev/null || echo 'Unable to determine')"
echo "DNS Servers: $(cat /etc/resolv.conf 2>/dev/null | grep nameserver | awk '{print $2}' | tr '\n' ' ' || echo 'Unable to determine')"

echo ""
echo "6. Firewall Status..."
if command -v ufw >/dev/null 2>&1; then
    echo "UFW Status: $(sudo ufw status 2>/dev/null || echo 'Unable to check')"
elif command -v firewall-cmd >/dev/null 2>&1; then
    echo "Firewalld Status: $(sudo firewall-cmd --state 2>/dev/null || echo 'Unable to check')"
else
    echo "Firewall: No common firewall tools detected"
fi

echo ""
echo "=== Troubleshooting Recommendations ==="

# Check if any tests failed and provide recommendations
if ! test_connectivity "https://github.com" "GitHub" >/dev/null 2>&1; then
    echo ""
    echo "❌ GitHub connectivity issues detected:"
    echo "   • Check your internet connection"
    echo "   • Try using a VPN"
    echo "   • Check if GitHub is blocked by your network"
    echo "   • Alternative: Download repository as ZIP file"
fi

if ! test_dns "github.com" >/dev/null 2>&1; then
    echo ""
    echo "❌ DNS resolution issues detected:"
    echo "   • Try using public DNS servers (*******, *******)"
    echo "   • Flush DNS cache"
    echo "   • Check /etc/resolv.conf configuration"
fi

echo ""
echo "=== Alternative Download Methods ==="
echo "If git clone fails, try these alternatives:"
echo ""
echo "1. Download as ZIP (Main Repository):"
echo "   curl -L -o hummingbot.zip https://github.com/hummingbot/hummingbot/archive/refs/heads/master.zip"
echo "   unzip hummingbot.zip"
echo "   mv hummingbot-master/* ."
echo ""
echo "2. Download as ZIP (Deploy Repository):"
echo "   curl -L -o deploy.zip https://github.com/hummingbot/deploy/archive/refs/heads/main.zip"
echo "   unzip deploy.zip"
echo "   mv deploy-main/* ."
echo ""
echo "3. Use shallow clone (faster):"
echo "   git clone --depth 1 https://github.com/hummingbot/hummingbot.git"
echo ""
echo "4. Use different protocol:"
echo "   <NAME_EMAIL>:hummingbot/hummingbot.git"

echo ""
echo "=== Docker Troubleshooting ==="
echo "If Docker issues occur:"
echo "• Ensure Docker Desktop is running"
echo "• Check available disk space: df -h"
echo "• Restart Docker: sudo systemctl restart docker (Linux)"
echo "• Clear Docker cache: docker system prune -a"
echo "• Check Docker logs: docker logs <container_name>"

echo ""
echo "=== Port Requirements ==="
echo "Ensure these ports are available:"
echo "• 8501 - Hummingbot Dashboard"
echo "• 8000 - Backend API"
echo "• 15888 - Gateway (for DEX trading)"
echo "• 1883 - MQTT Broker"

echo ""
echo "Troubleshooting complete. Check the recommendations above."
echo "For additional help, visit: https://discord.gg/hummingbot"
